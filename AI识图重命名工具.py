import asyncio
import aiohttp
import tkinter as tk
from tkinter import filedialog
import os
import sys
import threading
import time
import datetime
import colorama
from colorama import Fore, Style
from PIL import Image
import warnings
import base64
import io
import re

# 初始化colorama
colorama.init()

# 禁用SSL警告
warnings.filterwarnings('ignore', message='Unverified HTTPS request')

# AI模型API配置
AI_URL = "https://api.siliconflow.cn/v1/chat/completions"
AI_HEADERS_TEMPLATE = {
    "Authorization": "Bearer {api_key}",
    "Content-Type": "application/json"
}
AI_PAYLOAD_TEMPLATE = {
    "model": "THUDM/GLM-4.1V-9B-Thinking",
    "stream": False,
    "max_tokens": 128,
    "min_p": 0.05,
    "temperature": 0.3,
    "top_p": 0.8,
    "top_k": 40,
    "frequency_penalty": 0.3,
    "n": 1,
    "stop": []
}

def get_exe_directory():
    """获取程序所在目录"""
    if getattr(sys, 'frozen', False):
        return os.path.dirname(sys.executable)
    else:
        return os.path.dirname(os.path.abspath(__file__))

# 统计信息类
class RenameStats:
    def __init__(self):
        self.lock = threading.Lock()
        self.successful_renames = 0
        self.failed_renames = 0
        self.successful_ai_calls = 0
        self.failed_ai_calls = 0
        self.retry_attempts = 0
        self.retry_successes = 0
        self.start_time = time.time()
        self.total_input_tokens = 0
        self.total_output_tokens = 0
        self.api_key_usage = {}
        self.last_update_time = time.time()

    def increment_successful_rename(self):
        with self.lock:
            self.successful_renames += 1

    def increment_failed_rename(self):
        with self.lock:
            self.failed_renames += 1

    def increment_successful_ai_call(self, api_key):
        with self.lock:
            self.successful_ai_calls += 1
            self.api_key_usage[api_key] = self.api_key_usage.get(api_key, 0) + 1

    def increment_failed_ai_call(self):
        with self.lock:
            self.failed_ai_calls += 1

    def increment_retry_attempt(self):
        with self.lock:
            self.retry_attempts += 1

    def increment_retry_success(self):
        with self.lock:
            self.retry_successes += 1

    def add_token_usage(self, input_tokens, output_tokens):
        with self.lock:
            self.total_input_tokens += input_tokens
            self.total_output_tokens += output_tokens

    def get_elapsed_time(self):
        return time.time() - self.start_time

    def calculate_cost(self):
        """计算总成本（人民币）"""
        input_cost = (self.total_input_tokens / 1000000) * 0.25
        output_cost = (self.total_output_tokens / 1000000) * 1.0
        total_cost = input_cost + output_cost
        return input_cost, output_cost, total_cost
    
    def get_current_stats(self):
        """获取当前统计信息"""
        with self.lock:
            elapsed = self.get_elapsed_time()
            total_processed = self.successful_renames + self.failed_renames
            success_rate = (self.successful_renames / total_processed * 100) if total_processed > 0 else 0
            avg_time = elapsed / total_processed if total_processed > 0 else 0
            total_tokens = self.total_input_tokens + self.total_output_tokens
            _, _, total_cost = self.calculate_cost()
            
            return {
                'successful': self.successful_renames,
                'failed': self.failed_renames,
                'success_rate': success_rate,
                'elapsed': elapsed,
                'avg_time': avg_time,
                'total_tokens': total_tokens,
                'total_cost': total_cost,
                'api_keys_used': len(self.api_key_usage)
            }
    
    def print_dynamic_stats(self, current_index, total_files):
        """动态打印统计信息"""
        current_time = time.time()
        # 每2秒更新一次显示
        if current_time - self.last_update_time < 2:
            return
        
        self.last_update_time = current_time
        
        stats_data = self.get_current_stats()
        elapsed = stats_data['elapsed']
        minutes, seconds = divmod(elapsed, 60)
        
        # 清除当前行并打印统计信息
        print(f"\r{' ' * 100}", end='')  # 清除行
        print(f"\r📊 进度: {current_index}/{total_files} | "
              f"✅ {stats_data['successful']} | "
              f"❌ {stats_data['failed']} | "
              f"📈 {stats_data['success_rate']:.1f}% | "
              f"⏱️ {int(minutes)}:{int(seconds):02d} | "
              f"💰 {stats_data['total_tokens']:,} tokens | "
              f"💸 ￥{stats_data['total_cost']:.4f}", end='', flush=True)
    
    def print_summary(self):
        elapsed = self.get_elapsed_time()
        minutes, seconds = divmod(elapsed, 60)

        print("\n\n" + "="*60)
        print(f"{Fore.CYAN}处理完成统计{Style.RESET_ALL}".center(60))
        print("="*60)
        
        # 核心统计信息
        success_rate = (self.successful_renames / (self.successful_renames + self.failed_renames) * 100) if (self.successful_renames + self.failed_renames) > 0 else 0
        print(f"✅ 成功重命名: {Fore.GREEN}{self.successful_renames}{Style.RESET_ALL} 张")
        print(f"❌ 重命名失败: {Fore.RED}{self.failed_renames}{Style.RESET_ALL} 张")
        print(f"📊 成功率: {Fore.YELLOW}{success_rate:.1f}%{Style.RESET_ALL}")
        print(f"⏱️  总耗时: {Fore.YELLOW}{int(minutes)}分{int(seconds)}秒{Style.RESET_ALL}")

        # 重试统计信息
        if self.retry_attempts > 0:
            retry_success_rate = (self.retry_successes / self.retry_attempts * 100) if self.retry_attempts > 0 else 0
            print(f"🔄 重试次数: {Fore.CYAN}{self.retry_attempts}{Style.RESET_ALL} 次")
            print(f"🎯 重试成功: {Fore.GREEN}{self.retry_successes}{Style.RESET_ALL} 次")
            print(f"📈 重试成功率: {Fore.YELLOW}{retry_success_rate:.1f}%{Style.RESET_ALL}")
        
        if self.successful_ai_calls > 0:
            avg_time = elapsed / self.successful_ai_calls
            print(f"⚡ 平均处理时间: {Fore.YELLOW}{avg_time:.1f}秒/张{Style.RESET_ALL}")

        # Token和成本统计
        total_tokens = self.total_input_tokens + self.total_output_tokens
        _, _, total_cost = self.calculate_cost()
        
        print(f"\n💰 Token使用: {Fore.CYAN}{total_tokens:,}{Style.RESET_ALL} 个")
        print(f"💸 总成本: {Fore.GREEN}￥{total_cost:.4f}{Style.RESET_ALL}")
        
        # API密钥使用分布
        if len(self.api_key_usage) > 1:
            print(f"\n🔑 使用了 {len(self.api_key_usage)} 个API密钥")
        
        print("="*60)

# 日志系统类
class LogManager:
    def __init__(self):
        self.lock = threading.Lock()
        self.log_file = None
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志系统"""
        try:
            # 创建日志文件名（基于当前时间）
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            exe_dir = get_exe_directory()
            log_filename = f"AI识图重命名_日志_{timestamp}.log"
            self.log_file = os.path.join(exe_dir, log_filename)
            
            # 确保目录存在
            os.makedirs(exe_dir, exist_ok=True)
            
            # 创建日志文件并写入头部信息
            with open(self.log_file, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("🚀 AI识图重命名工具 - 详细日志\n")
                f.write(f"📅 启动时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 80 + "\n\n")
                f.flush()  # 强制刷新缓冲区
            
            # 验证文件是否成功创建
            if os.path.exists(self.log_file):
                print(f"📝 日志文件: {Fore.CYAN}{os.path.basename(self.log_file)}{Style.RESET_ALL}")
            else:
                print(f"❌ 日志文件创建失败")
                self.log_file = None
            
        except Exception as e:
            print(f"❌ 日志系统初始化失败: {e}")
            self.log_file = None
    
    def write_log(self, level, message):
        """写入日志"""
        with self.lock:
            try:
                if not self.log_file:
                    return
                    
                timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                log_entry = f"[{timestamp}] [{level}] {message}\n"
                
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    f.write(log_entry)
                    f.flush()  # 强制刷新缓冲区，确保立即写入
                    os.fsync(f.fileno())  # 强制同步到磁盘
            except Exception as e:
                # 如果日志写入失败，至少在控制台显示错误
                print(f"日志写入失败: {e}")
    
    def log_info(self, message):
        """记录信息日志"""
        self.write_log("信息", message)
    
    def log_error(self, message):
        """记录错误日志"""
        self.write_log("错误", message)
    
    def log_warning(self, message):
        """记录警告日志"""
        self.write_log("警告", message)
    
    def log_system_info(self, api_keys_count, folder_path, image_count):
        """记录系统初始化信息"""
        self.log_info("🔧 系统初始化")
        self.log_info(f"   🔑 加载API密钥数量: {api_keys_count}")
        self.log_info(f"   📁 选择文件夹: {folder_path}")
        self.log_info(f"   🖼️ 发现图片数量: {image_count}")
        self.log_info("")
    
    def log_api_request(self, image_path, api_key_suffix, attempt):
        """记录API请求"""
        filename = os.path.basename(image_path)
        self.log_info(f"🔄 API请求开始")
        self.log_info(f"   📁 文件名: {filename}")
        self.log_info(f"   🔑 API密钥: ...{api_key_suffix}")
        self.log_info(f"   🔢 尝试次数: {attempt + 1}")
    
    def log_api_response(self, image_path, raw_response, cleaned_response, tokens_info):
        """记录API响应"""
        filename = os.path.basename(image_path)
        self.log_info(f"✅ API响应成功")
        self.log_info(f"   📁 文件名: {filename}")
        self.log_info(f"   📝 AI原始回答: {raw_response}")
        self.log_info(f"   🧹 清理后描述: {cleaned_response}")
        if tokens_info:
            total_tokens = tokens_info.get('input', 0) + tokens_info.get('output', 0)
            self.log_info(f"   💰 Token使用: 输入{tokens_info.get('input', 0)} + 输出{tokens_info.get('output', 0)} = 总计{total_tokens}")
        self.log_info("")
    
    def log_api_error(self, image_path, error_msg, attempt):
        """记录API错误"""
        filename = os.path.basename(image_path)
        self.log_error(f"❌ API请求失败")
        self.log_error(f"   📁 文件名: {filename}")
        self.log_error(f"   🔢 尝试次数: {attempt + 1}")
        self.log_error(f"   ⚠️ 错误信息: {error_msg}")
        self.log_error("")
    
    def log_rename_success(self, old_path, new_path, description):
        """记录重命名成功"""
        old_name = os.path.basename(old_path)
        new_name = os.path.basename(new_path)
        self.log_info(f"✅ 文件重命名成功")
        self.log_info(f"   📁 原文件名: {old_name}")
        self.log_info(f"   📁 新文件名: {new_name}")
        self.log_info(f"   📝 描述内容: {description}")
        self.log_info("")
    
    def log_rename_error(self, image_path, error_msg):
        """记录重命名错误"""
        filename = os.path.basename(image_path)
        self.log_error(f"❌ 文件重命名失败")
        self.log_error(f"   📁 文件名: {filename}")
        self.log_error(f"   ⚠️ 错误信息: {error_msg}")
        self.log_error("")
    
    def log_progress_update(self, current, total, successful, failed):
        """记录进度更新"""
        success_rate = (successful / (successful + failed) * 100) if (successful + failed) > 0 else 0
        self.log_info(f"📊 进度更新: {current}/{total} | 成功: {successful} | 失败: {failed} | 成功率: {success_rate:.1f}%")
    
    def log_final_summary(self, stats_obj):
        """记录最终统计摘要"""
        self.log_info("=" * 60)
        self.log_info("📊 最终处理统计摘要")
        self.log_info("=" * 60)
        
        elapsed = stats_obj.get_elapsed_time()
        minutes, seconds = divmod(elapsed, 60)
        success_rate = (stats_obj.successful_renames / (stats_obj.successful_renames + stats_obj.failed_renames) * 100) if (stats_obj.successful_renames + stats_obj.failed_renames) > 0 else 0
        
        self.log_info(f"✅ 成功重命名: {stats_obj.successful_renames} 张")
        self.log_info(f"❌ 重命名失败: {stats_obj.failed_renames} 张")
        self.log_info(f"📈 成功率: {success_rate:.1f}%")
        self.log_info(f"⏱️ 总耗时: {int(minutes)}分{int(seconds)}秒")
        
        if stats_obj.successful_ai_calls > 0:
            avg_time = elapsed / stats_obj.successful_ai_calls
            self.log_info(f"⚡ 平均处理时间: {avg_time:.1f}秒/张")
        
        total_tokens = stats_obj.total_input_tokens + stats_obj.total_output_tokens
        _, _, total_cost = stats_obj.calculate_cost()
        self.log_info(f"💰 Token总使用量: {total_tokens:,} 个")
        self.log_info(f"💸 总成本: ￥{total_cost:.4f}")
        self.log_info(f"🔑 使用API密钥数量: {len(stats_obj.api_key_usage)}")

        # 重试统计信息
        if stats_obj.retry_attempts > 0:
            retry_success_rate = (stats_obj.retry_successes / stats_obj.retry_attempts * 100) if stats_obj.retry_attempts > 0 else 0
            self.log_info(f"🔄 重试总次数: {stats_obj.retry_attempts}")
            self.log_info(f"🎯 重试成功次数: {stats_obj.retry_successes}")
            self.log_info(f"📈 重试成功率: {retry_success_rate:.1f}%")

        # API密钥使用详情
        if len(stats_obj.api_key_usage) > 1:
            self.log_info("🔑 API密钥使用分布:")
            for api_key, count in stats_obj.api_key_usage.items():
                key_suffix = api_key[-8:] if len(api_key) > 8 else api_key
                self.log_info(f"   ...{key_suffix}: {count} 次")
        
        self.log_info("=" * 60)
        self.log_info("🎉 处理任务完成")
        self.log_info("=" * 60)

# 全局对象
stats = RenameStats()
logger = LogManager()

def load_api_keys():
    """加载API密钥"""
    keys = []
    try:
        exe_dir = get_exe_directory()
        api_keys_path = os.path.join(exe_dir, 'api_keys.txt')
        
        with open(api_keys_path, 'r', encoding='utf-8') as f:
            for line in f:
                key = line.strip()
                if key and not key.startswith('#'):
                    if len(key) > 20:
                        keys.append(key)
        
        print(f"🔑 加载了 {Fore.GREEN}{len(keys)}{Style.RESET_ALL} 个API密钥")
        return keys
    except FileNotFoundError:
        print(f"{Fore.RED}❌ 未找到api_keys.txt文件{Style.RESET_ALL}")
        return []
    except Exception as e:
        print(f"{Fore.RED}❌ 加载API密钥失败: {e}{Style.RESET_ALL}")
        return []

def select_image_folder():
    """选择图片文件夹"""
    try:
        root = tk.Tk()
        root.withdraw()
        root.attributes('-topmost', True)
        folder_path = filedialog.askdirectory(
            title="选择包含图片的文件夹",
            mustexist=True
        )
        root.destroy()
        
        if not folder_path:
            return None
        
        if not os.path.exists(folder_path) or not os.access(folder_path, os.R_OK):
            print(f"{Fore.RED}❌ 文件夹不存在或无法访问{Style.RESET_ALL}")
            return None
            
        print(f"📁 选择文件夹: {Fore.CYAN}{os.path.basename(folder_path)}{Style.RESET_ALL}")
        return folder_path
    except Exception as e:
        print(f"{Fore.RED}❌ 选择文件夹失败: {e}{Style.RESET_ALL}")
        return None

def get_image_files(folder_path):
    """获取文件夹中的所有图片文件"""
    image_extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.webp', '.tiff', '.tif')
    image_files = []
    
    try:
        # 直接扫描当前文件夹，不搜索子文件夹
        for file in os.listdir(folder_path):
            if file.lower().endswith(image_extensions):
                full_path = os.path.join(folder_path, file)
                if os.path.isfile(full_path):
                    image_files.append(full_path)
        
        image_files.sort()
        print(f"🖼️  找到 {Fore.GREEN}{len(image_files)}{Style.RESET_ALL} 张图片")
        
        return image_files
    except Exception as e:
        print(f"{Fore.RED}❌ 扫描图片失败: {e}{Style.RESET_ALL}")
        return []

def convert_image_to_base64(image_path, max_size=(1024, 1024)):
    """将图片转换为base64编码"""
    try:
        with Image.open(image_path) as img:
            if img.mode in ('RGBA', 'LA', 'P'):
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background
            elif img.mode != 'RGB':
                img = img.convert('RGB')
            
            if img.size[0] > max_size[0] or img.size[1] > max_size[1]:
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            buffer = io.BytesIO()
            img.save(buffer, format="WEBP", quality=80, optimize=True)
            base64_image = base64.b64encode(buffer.getvalue()).decode('utf-8')
            return f"data:image/webp;base64,{base64_image}"
    except Exception:
        return None

def clean_ai_response(response):
    """清理AI回答，确保只保留英文单词"""
    try:
        if not response:
            return "unnamed_image"
        
        # 移除所有换行符、制表符等空白字符
        cleaned = re.sub(r'\s+', ' ', response.strip())
        
        # 只保留英文字母、数字和空格
        cleaned = re.sub(r'[^a-zA-Z0-9\s]', '', cleaned)
        
        # 分割成单词，只保留英文单词
        words = []
        for word in cleaned.split():
            # 只保留包含英文字母的单词
            if re.search(r'[a-zA-Z]', word):
                words.append(word.lower())
        
        # 限制单词数量（最多15个单词）
        if len(words) > 15:
            words = words[:15]
        
        # 重新组合成单行
        result = ' '.join(words)
        
        # 如果结果为空或太短，使用默认名称
        if not result or len(result) < 3:
            return "unnamed_image"
        
        return result
    except Exception:
        return "unnamed_image"

def clean_filename(description):
    """将描述转换为适合的文件名"""
    try:
        if not description:
            return "unnamed_image"
        
        # 将空格替换为下划线
        cleaned = description.replace(' ', '_')
        
        # 移除任何剩余的特殊字符
        cleaned = re.sub(r'[^\w]', '', cleaned)
        
        # 限制长度
        if len(cleaned) > 80:
            cleaned = cleaned[:80]
        
        if not cleaned:
            cleaned = "unnamed_image"
        
        # 确保不以数字开头
        if cleaned[0].isdigit():
            cleaned = f"img_{cleaned}"
        
        return cleaned
    except Exception:
        return "unnamed_image"

async def get_image_description(image_path, api_key, session=None):
    """使用AI获取图片描述"""
    max_retries = 2
    retry_delay = [1, 3]

    if session is None:
        connector = aiohttp.TCPConnector(ssl=False)
        session = aiohttp.ClientSession(connector=connector)
        should_close_session = True
    else:
        should_close_session = False

    api_key_suffix = api_key[-8:] if len(api_key) > 8 else api_key

    try:
        for attempt in range(max_retries + 1):
            try:
                # 记录API请求开始
                logger.log_api_request(image_path, api_key_suffix, attempt)

                base64_img = convert_image_to_base64(image_path)
                if not base64_img:
                    logger.log_api_error(image_path, "图片转换base64失败", attempt)
                    stats.increment_failed_ai_call()
                    return None

                messages = [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": base64_img,
                                    "detail": "high"
                                }
                            },
                            {
                                "type": "text",
                                "text": "使用英文生成一个少于18个单词的单行图片内容描述输出，不含任何标点符号、换行符、分隔符或额外解释文本，仅输出图片描述内容本身。"
                            }
                        ]
                    }
                ]

                headers = AI_HEADERS_TEMPLATE.copy()
                headers["Authorization"] = f"Bearer {api_key}"

                payload = AI_PAYLOAD_TEMPLATE.copy()
                payload["messages"] = messages

                timeout = aiohttp.ClientTimeout(total=30)
                async with session.post(AI_URL, json=payload, headers=headers, timeout=timeout) as resp:
                    resp.raise_for_status()
                    data = await resp.json()

                if "choices" in data and data["choices"]:
                    raw_result = data["choices"][0]["message"]["content"].strip()

                    # 立即清理AI回答，确保只保留英文
                    cleaned_result = clean_ai_response(raw_result)

                    # 准备token信息
                    tokens_info = None
                    if "usage" in data:
                        input_tokens = data["usage"].get("prompt_tokens", 0)
                        output_tokens = data["usage"].get("completion_tokens", 0)
                        tokens_info = {"input": input_tokens, "output": output_tokens}
                        stats.add_token_usage(input_tokens, output_tokens)

                    # 记录API响应成功
                    logger.log_api_response(image_path, raw_result, cleaned_result, tokens_info)

                    stats.increment_successful_ai_call(api_key)
                    return cleaned_result

            except Exception as e:
                error_msg = str(e)
                logger.log_api_error(image_path, error_msg, attempt)

            if attempt < max_retries:
                await asyncio.sleep(retry_delay[attempt])

        stats.increment_failed_ai_call()
        return None
    finally:
        if should_close_session:
            await session.close()

def rename_image_with_description(image_path, description):
    """使用描述重命名图片文件"""
    try:
        directory = os.path.dirname(image_path)
        _, ext = os.path.splitext(image_path)
        
        clean_name = clean_filename(description)
        new_filename = f"{clean_name}{ext.lower()}"
        new_path = os.path.join(directory, new_filename)
        
        # 如果目标路径与原路径相同，直接返回成功（无需重命名）
        if os.path.abspath(image_path) == os.path.abspath(new_path):
            logger.log_rename_success(image_path, new_path, description)
            stats.increment_successful_rename()
            return True
        
        counter = 1
        original_clean_name = clean_name
        # 检查目标文件是否存在（排除原文件本身）
        while os.path.exists(new_path) and os.path.abspath(new_path) != os.path.abspath(image_path):
            clean_name = f"{original_clean_name}_{counter:03d}"
            new_filename = f"{clean_name}{ext.lower()}"
            new_path = os.path.join(directory, new_filename)
            counter += 1
            
            if counter > 999:
                clean_name = f"{original_clean_name}_{int(time.time())}"
                new_filename = f"{clean_name}{ext.lower()}"
                new_path = os.path.join(directory, new_filename)
                break
        
        os.rename(image_path, new_path)
        
        # 记录重命名成功
        logger.log_rename_success(image_path, new_path, description)
        stats.increment_successful_rename()
        return True
        
    except Exception as e:
        # 记录重命名失败
        logger.log_rename_error(image_path, str(e))
        stats.increment_failed_rename()
        return False

async def process_single_image(image_path, api_key, session=None):
    """处理单张图片的完整流程，支持失败重试机制"""
    should_close_session = False
    if session is None:
        connector = aiohttp.TCPConnector(ssl=False)
        session = aiohttp.ClientSession(connector=connector)
        should_close_session = True

    try:
        description = await get_image_description(image_path, api_key, session)

        if description:
            success = rename_image_with_description(image_path, description)
            return success, os.path.basename(image_path), description[:30] + "..." if len(description) > 30 else description
        else:
            stats.increment_failed_rename()
            return False, os.path.basename(image_path), "AI识别失败"

    except Exception:
        stats.increment_failed_rename()
        return False, os.path.basename(image_path), "处理异常"
    finally:
        if should_close_session:
            await session.close()

async def process_single_image_with_retry(image_path, primary_api_key, all_api_keys, session, max_retry_attempts=2):
    """处理单张图片，支持使用不同API密钥重试"""
    # 首次尝试使用主API密钥
    success, filename, description = await process_single_image(image_path, primary_api_key, session)

    if success:
        return success, filename, description

    # 如果失败且有多个API密钥，尝试使用其他密钥重试
    if len(all_api_keys) > 1 and max_retry_attempts > 0:
        logger.log_info(f"🔄 开始重试处理失败的图片: {filename}")

        # 获取除主密钥外的其他密钥
        retry_keys = [key for key in all_api_keys if key != primary_api_key]

        for attempt in range(min(max_retry_attempts, len(retry_keys))):
            stats.increment_retry_attempt()  # 统计重试次数

            retry_api_key = retry_keys[attempt]
            retry_key_suffix = retry_api_key[-8:] if len(retry_api_key) > 8 else retry_api_key

            logger.log_info(f"🔄 重试 {attempt + 1}/{max_retry_attempts}: {filename} 使用API密钥 ...{retry_key_suffix}")

            # 使用不同的API密钥重试
            retry_success, retry_filename, retry_description = await process_single_image(image_path, retry_api_key, session)

            if retry_success:
                stats.increment_retry_success()  # 统计重试成功次数
                logger.log_info(f"✅ 重试成功: {filename} -> {retry_description}")
                return retry_success, retry_filename, f"重试成功: {retry_description}"
            else:
                logger.log_warning(f"❌ 重试失败 {attempt + 1}: {filename}")

        logger.log_error(f"❌ 所有重试均失败: {filename}")

    return success, filename, description

async def main():
    """主函数"""
    # 简洁的标题
    print(f"\n{Fore.CYAN}🤖 AI识图重命名工具{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}{Style.RESET_ALL}")
    print("-" * 40)

    # 加载API密钥
    api_keys = load_api_keys()
    if not api_keys:
        logger.log_error("未找到API密钥文件或密钥为空")
        input(f"{Fore.RED}❌ 未找到API密钥，按回车退出...{Style.RESET_ALL}")
        return

    # 选择图片文件夹
    folder_path = select_image_folder()
    if not folder_path:
        logger.log_warning("用户未选择文件夹，程序退出")
        return

    # 获取所有图片文件
    image_files = get_image_files(folder_path)
    if not image_files:
        logger.log_error(f"在文件夹 {folder_path} 中未找到图片文件")
        input(f"{Fore.RED}❌ 未找到图片文件，按回车退出...{Style.RESET_ALL}")
        return

    # 记录系统初始化信息
    logger.log_system_info(len(api_keys), folder_path, len(image_files))

    print(f"\n🚀 开始处理...")
    print(f"📊 实时统计信息:")

    # 使用异步并发处理 - 移除50线程限制，拉满性能
    max_concurrent = min(len(api_keys) * 2, len(image_files), 100)  # 限制最大并发数为100
    logger.log_info(f"🔧 使用异步并发处理，最大并发数: {max_concurrent} (已移除50线程限制)")
    logger.log_info(f"🔄 失败重试机制已启用，最多重试2次，使用不同API密钥")

    failed_items = []
    completed_count = 0

    try:
        # 创建共享的aiohttp会话
        connector = aiohttp.TCPConnector(ssl=False, limit=max_concurrent, limit_per_host=max_concurrent)
        async with aiohttp.ClientSession(connector=connector) as session:

            # 启动动态显示线程
            def update_display():
                while completed_count < len(image_files):
                    # 使用RenameStats中的统计数据，但显示正确的completed_count
                    current_time = time.time()
                    if current_time - stats.last_update_time >= 2:
                        stats.last_update_time = current_time
                        stats_data = stats.get_current_stats()
                        elapsed = stats_data['elapsed']
                        minutes, seconds = divmod(elapsed, 60)

                        # 清除当前行并打印统计信息
                        print(f"\r{' ' * 100}", end='')  # 清除行
                        print(f"\r📊 进度: {completed_count}/{len(image_files)} | "
                              f"✅ {stats_data['successful']} | "
                              f"❌ {stats_data['failed']} | "
                              f"📈 {stats_data['success_rate']:.1f}% | "
                              f"⏱️ {int(minutes)}:{int(seconds):02d} | "
                              f"💰 {stats_data['total_tokens']:,} tokens | "
                              f"💸 ￥{stats_data['total_cost']:.4f}", end='', flush=True)
                    time.sleep(1)

            display_thread = threading.Thread(target=update_display, daemon=True)
            display_thread.start()

            # 创建信号量来控制并发数
            semaphore = asyncio.Semaphore(max_concurrent)

            async def process_with_semaphore(image_path, primary_api_key):
                async with semaphore:
                    return await process_single_image_with_retry(image_path, primary_api_key, api_keys, session, 2)

            # 创建所有任务
            tasks = []
            for i, image_path in enumerate(image_files):
                primary_api_key = api_keys[i % len(api_keys)]
                task = asyncio.create_task(process_with_semaphore(image_path, primary_api_key))
                tasks.append((task, image_path))

            # 等待所有任务完成
            for task, image_path in tasks:
                try:
                    success, filename, description = await task
                    if not success:
                        failed_items.append((filename, description))
                except Exception as e:
                    failed_items.append((os.path.basename(image_path), "处理异常"))
                    logger.log_error(f"处理图片异常: {os.path.basename(image_path)} - {str(e)}")

                completed_count += 1

                # 每处理10张图片记录一次进度
                if completed_count % 10 == 0:
                    current_stats = stats.get_current_stats()
                    logger.log_progress_update(completed_count, len(image_files), current_stats['successful'], current_stats['failed'])

                # 每处理10张图片显示一次详细进度
                if completed_count % 10 == 0 or completed_count == len(image_files):
                    print(f"\n🔄 已处理 {completed_count}/{len(image_files)} 张图片")
    
    except KeyboardInterrupt:
        logger.log_warning("用户中断处理")
        print(f"\n{Fore.YELLOW}⚠️  用户中断处理{Style.RESET_ALL}")
    finally:
        # 确保最后一次显示完整统计
        print(f"\r{' ' * 100}", end='')  # 清除动态显示行
    
    # 记录失败项目
    if failed_items:
        logger.log_error(f"共有 {len(failed_items)} 个文件处理失败")
        for filename, reason in failed_items:
            logger.log_error(f"失败文件: {filename} - 原因: {reason}")
    
    # 显示失败的项目（如果有且数量不多）
    if failed_items and len(failed_items) <= 10:
        print(f"\n{Fore.RED}❌ 处理失败的文件:{Style.RESET_ALL}")
        for filename, reason in failed_items:
            print(f"   • {filename} - {reason}")
    elif len(failed_items) > 10:
        print(f"\n{Fore.RED}❌ 共有 {len(failed_items)} 个文件处理失败{Style.RESET_ALL}")
    
    # 记录最终统计
    logger.log_final_summary(stats)
    
    # 打印统计结果
    stats.print_summary()
    
    print(f"\n{Fore.GREEN}✅ 任务完成！{Style.RESET_ALL}")
    
    # 安全显示日志文件路径
    if logger.log_file and os.path.exists(logger.log_file):
        print(f"📝 详细日志已保存到: {Fore.CYAN}{os.path.basename(logger.log_file)}{Style.RESET_ALL}")
    else:
        print(f"📝 日志文件保存可能出现问题")
    
    input(f"按回车键退出...")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}⚠️  程序被中断{Style.RESET_ALL}")
        stats.print_summary()
    except Exception as e:
        print(f"\n{Fore.RED}❌ 程序错误: {e}{Style.RESET_ALL}")
    finally:
        print(Style.RESET_ALL)