#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI识图重命名工具 - 自动打包脚本
确保不会将敏感文件（api_keys.txt, key.vdf）打包进exe
"""

import os
import sys
import shutil
import subprocess
import datetime
from pathlib import Path

def print_colored(text, color="white"):
    """打印彩色文本"""
    colors = {
        "red": "\033[91m",
        "green": "\033[92m", 
        "yellow": "\033[93m",
        "blue": "\033[94m",
        "cyan": "\033[96m",
        "white": "\033[97m",
        "reset": "\033[0m"
    }
    print(f"{colors.get(color, colors['white'])}{text}{colors['reset']}")

def check_requirements():
    """检查打包环境"""
    print_colored("🔍 检查打包环境...", "cyan")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"   Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查必要的包
    required_packages = [
        "pyinstaller",
        "requests", 
        "pillow",
        "colorama",
        "tqdm"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print_colored(f"   ✅ {package}", "green")
        except ImportError:
            print_colored(f"   ❌ {package}", "red")
            missing_packages.append(package)
    
    if missing_packages:
        print_colored(f"\n❌ 缺少必要的包: {', '.join(missing_packages)}", "red")
        print_colored("请先安装缺少的包:", "yellow")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def backup_sensitive_files():
    """备份敏感文件"""
    print_colored("🔒 备份敏感文件...", "cyan")
    
    sensitive_files = ["api_keys.txt", "key.vdf"]
    backup_dir = "敏感文件备份"
    
    # 创建备份目录
    if os.path.exists(backup_dir):
        shutil.rmtree(backup_dir)
    os.makedirs(backup_dir)
    
    backed_up_files = []
    for file in sensitive_files:
        if os.path.exists(file):
            backup_path = os.path.join(backup_dir, file)
            shutil.copy2(file, backup_path)
            backed_up_files.append(file)
            print_colored(f"   ✅ 备份: {file}", "green")
        else:
            print_colored(f"   ⚠️  未找到: {file}", "yellow")
    
    return backed_up_files, backup_dir

def create_spec_file():
    """创建或更新spec文件"""
    print_colored("📝 创建打包配置文件...", "cyan")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
"""
AI识图重命名工具 PyInstaller 配置文件
确保不打包敏感文件
"""

block_cipher = None

a = Analysis(
    ['AI识图重命名工具.py'],
    pathex=[],
    binaries=[],
    datas=[],  # 明确不包含任何数据文件，避免打包敏感文件
    hiddenimports=[
        'PIL._tkinter_finder',
        'tkinter',
        'tkinter.filedialog', 
        'tkinter.messagebox',
        'requests',
        'requests.adapters',
        'requests.packages',
        'requests.packages.urllib3',
        'requests.packages.urllib3.util',
        'requests.packages.urllib3.util.retry',
        'colorama',
        'tqdm',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'PIL.ImageFile',
        'concurrent.futures',
        'threading',
        'queue',
        'json',
        'logging',
        'base64',
        'io',
        're',
        'datetime',
        'warnings',
        'time',
        'random',
        'os',
        'sys'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的模块以减小体积
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'jupyter',
        'IPython',
        'notebook'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 明确排除敏感文件
sensitive_files = ['api_keys.txt', 'key.vdf']
a.datas = [x for x in a.datas if not any(sf in x[0] for sf in sensitive_files)]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AI识图重命名工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 保持控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
'''
    
    with open("AI识图重命名工具.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print_colored("   ✅ 配置文件已创建", "green")

def build_exe():
    """执行打包"""
    print_colored("🔨 开始打包...", "cyan")
    
    # 清理之前的构建文件
    cleanup_dirs = ["build", "dist", "__pycache__"]
    for dir_name in cleanup_dirs:
        if os.path.exists(dir_name):
            print_colored(f"   🧹 清理: {dir_name}", "yellow")
            shutil.rmtree(dir_name)
    
    # 执行PyInstaller
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--clean",  # 清理缓存
        "--noconfirm",  # 不询问覆盖
        "AI识图重命名工具.spec"
    ]
    
    print_colored(f"   执行命令: {' '.join(cmd)}", "blue")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print_colored("   ✅ 打包成功!", "green")
            return True
        else:
            print_colored("   ❌ 打包失败!", "red")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print_colored(f"   ❌ 打包异常: {e}", "red")
        return False

def verify_exe():
    """验证生成的exe文件"""
    print_colored("🔍 验证生成的exe文件...", "cyan")
    
    exe_path = os.path.join("dist", "AI识图重命名工具.exe")
    
    if not os.path.exists(exe_path):
        print_colored("   ❌ 未找到生成的exe文件", "red")
        return False
    
    # 检查文件大小
    file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
    print_colored(f"   📦 文件大小: {file_size:.1f} MB", "blue")
    
    # 检查是否包含敏感文件（简单检查）
    print_colored("   🔒 检查敏感文件是否被排除...", "blue")
    
    # 这里可以添加更详细的检查逻辑
    # 比如使用strings命令或其他工具检查exe内容
    
    print_colored("   ✅ exe文件验证完成", "green")
    return True

def restore_sensitive_files(backed_up_files, backup_dir):
    """恢复敏感文件"""
    print_colored("🔄 恢复敏感文件...", "cyan")
    
    for file in backed_up_files:
        backup_path = os.path.join(backup_dir, file)
        if os.path.exists(backup_path):
            shutil.copy2(backup_path, file)
            print_colored(f"   ✅ 恢复: {file}", "green")
    
    # 清理备份目录
    if os.path.exists(backup_dir):
        shutil.rmtree(backup_dir)
        print_colored(f"   🧹 清理备份目录: {backup_dir}", "yellow")

def create_distribution_package():
    """创建分发包"""
    print_colored("📦 创建分发包...", "cyan")
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    package_name = f"AI识图重命名工具_v{timestamp}"
    package_dir = os.path.join("发布包", package_name)
    
    # 创建发布目录
    os.makedirs(package_dir, exist_ok=True)
    
    # 复制exe文件
    exe_source = os.path.join("dist", "AI识图重命名工具.exe")
    exe_dest = os.path.join(package_dir, "AI识图重命名工具.exe")
    
    if os.path.exists(exe_source):
        shutil.copy2(exe_source, exe_dest)
        print_colored(f"   ✅ 复制exe文件", "green")
    
    # 创建使用说明
    readme_content = f"""# AI识图重命名工具 使用说明

## 版本信息
- 打包时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 版本: {timestamp}

## 使用前准备

### 1. 创建API密钥文件
在exe文件同目录下创建 `api_keys.txt` 文件，每行一个API密钥：
```
sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
sk-yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy
```

### 2. 可选：创建key.vdf文件
如果需要其他配置，可以创建 `key.vdf` 文件。

## 使用方法
1. 双击运行 `AI识图重命名工具.exe`
2. 选择包含图片的文件夹
3. 等待处理完成

## 注意事项
- 确保api_keys.txt文件存在且包含有效的API密钥
- 程序会在同目录生成详细的日志文件
- 支持的图片格式：png, jpg, jpeg, bmp, gif, webp, tiff, tif

## 故障排除
- 如果程序无法启动，请检查api_keys.txt文件是否存在
- 如果处理失败，请查看生成的日志文件
- 确保有足够的磁盘空间和网络连接

## 技术支持
如有问题，请查看详细日志文件或联系技术支持。
"""
    
    readme_path = os.path.join(package_dir, "使用说明.txt")
    with open(readme_path, "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print_colored(f"   ✅ 创建使用说明", "green")
    
    # 创建示例API密钥文件
    example_api_path = os.path.join(package_dir, "api_keys_示例.txt")
    with open(example_api_path, "w", encoding="utf-8") as f:
        f.write("# 请将此文件重命名为 api_keys.txt 并填入真实的API密钥\n")
        f.write("# 每行一个密钥，以sk-开头\n")
        f.write("# sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx\n")
        f.write("# sk-yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy\n")
    
    print_colored(f"   ✅ 创建示例配置文件", "green")
    print_colored(f"   📦 分发包位置: {package_dir}", "blue")
    
    return package_dir

def main():
    """主函数"""
    print_colored("🚀 AI识图重命名工具 - 自动打包脚本", "cyan")
    print_colored("=" * 50, "blue")
    
    try:
        # 1. 检查环境
        if not check_requirements():
            return False
        
        # 2. 备份敏感文件
        backed_up_files, backup_dir = backup_sensitive_files()
        
        # 3. 创建spec文件
        create_spec_file()
        
        # 4. 执行打包
        if not build_exe():
            return False
        
        # 5. 验证exe
        if not verify_exe():
            return False
        
        # 6. 创建分发包
        package_dir = create_distribution_package()
        
        # 7. 恢复敏感文件
        restore_sensitive_files(backed_up_files, backup_dir)
        
        print_colored("\n" + "=" * 50, "green")
        print_colored("🎉 打包完成!", "green")
        print_colored(f"📦 exe文件位置: dist/AI识图重命名工具.exe", "blue")
        print_colored(f"📦 分发包位置: {package_dir}", "blue")
        print_colored("=" * 50, "green")
        
        return True
        
    except KeyboardInterrupt:
        print_colored("\n⚠️  用户中断打包过程", "yellow")
        return False
    except Exception as e:
        print_colored(f"\n❌ 打包过程出现异常: {e}", "red")
        return False
    finally:
        # 确保恢复敏感文件
        if 'backed_up_files' in locals() and 'backup_dir' in locals():
            restore_sensitive_files(backed_up_files, backup_dir)

if __name__ == "__main__":
    success = main()
    input(f"\n按回车键退出...")
    sys.exit(0 if success else 1)
